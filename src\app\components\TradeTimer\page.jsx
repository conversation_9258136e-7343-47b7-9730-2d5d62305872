

"use client";
import { useState, useEffect } from "react";
import styles from "./tradeTimer.module.css";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
// import { useWebsocketContext } from "@/app/context/AuthContext";

const Page = ({ duration, orderNumber, onTimeIncreased }) => {
  // const { connection, sendMessage } = useWebsocketContext();
  const [time, setTime] = useState(duration || 0);
  const [stopTimer, setStopTimer] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [timerIncreased, setTimerIncreased] = useState(false);

  const handleTimeIncrease = async () => {
    const payload = {
      add_minutes: 4,
      order_id: orderNumber,
    };
    const res = await customFetchWithToken.post("trade/increase-time/", payload);
    if (res.status === 200) {
      toast.success("Trade time increased successfully");
      // Trigger refetch of trade order details
      if (onTimeIncreased) {
        onTimeIncreased();
      }
    } else {
      toast.error("Failed to increase trade time");
    }
    setShowConfirmation(false);
    setTimerIncreased(true);
  };

  const handleTimeReject = () => {
    setShowConfirmation(false);
  };

  const getFormattedTime = (milliseconds) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const totalMinutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    const minutes = totalMinutes % 60;
    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
  };

  // Combined timer and time check effect
  useEffect(() => {
    if (!duration || stopTimer) return;

    setTime(duration);
    const timer = setInterval(() => {
      setTime((prev) => {
        const newTime = prev - 1000;

        // Check for timer increase prompt at 9.5 minutes (570000ms = 9.5 minutes)
        // Use a range to account for timing precision issues
        // if (newTime <=  360000 570,000 && !timerIncreased) {
        if (newTime <= 570000 && newTime > 2000 && !timerIncreased) {
          console.log("TradeTimer: Showing popup at", newTime, "ms (9.5 minutes)");
          setShowConfirmation(true);
        }

        // Check for timer completion
        if (newTime <= 0) {
          clearInterval(timer);
          setStopTimer(true);
          return 0;
        }

        return newTime;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [duration, stopTimer, timerIncreased]);

  return (
    <>
      {showConfirmation && (
        <div className={styles.alertPopup}>
          <p>
            Your order {orderNumber}: Do you want to increase trade time by 4
            minutes?
          </p>
          <div className={styles.btnContainer}>
            <button className={styles.yesBtn} onClick={handleTimeIncrease}>
              Yes
            </button>
            <button className={styles.noBtn} onClick={handleTimeReject}>
              No
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default Page;
