"use client";
import { useState, useEffect, useRef } from "react";
import React from "react";

import styles from "./table.module.css";

import Modal from "react-modal";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import {
  exchangeRateCurrencyApi,
  exchangeRateCurrencyApi1,
} from "@/app/api/searchListing/searchAPI";
import { useRouter } from "next/navigation";
import RecipientConfirmModal from "../../components/RecipientConfirmModal/page";

import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import { Tooltip } from "react-tooltip";

const page = ({
  listingId,
  name,
  min_liquidity,
  max_liquidity,
  available_liquidity,
  payIn_option,
  payOut_option,
  rate,
  terms,
  time,
  payIn_currency,
  payOut_currency,
  time_limit,
  amount,
  highlightListingId,
}) => {
  const authTokenRef = useRef(null);

  let verifyStatus;
  if (typeof window !== "undefined") {
    verifyStatus = localStorage.getItem("verificationStatus");
  }

  let token;
  if (typeof window !== "undefined") {
    token = sessionStorage.getItem("user");
    if (token) {
      authTokenRef.current = token;
    }
  }

  const router = useRouter();
  const timestamp = time;

  const date = new Date(timestamp);

  const datePart = `${date.getFullYear()}-${(date.getMonth() + 1)
    .toString()
    .padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${date
    .getHours()
    .toString()
    .padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}:${date
    .getSeconds()
    .toString()
    .padStart(2, "0")}`;

  const customStyles = {
    content: {
      top: "50%",
      left: "50%",
      right: "auto",
      bottom: "auto",
      marginRight: "-50%",
      transform: "translate(-50%, -50%)",
      borderRadius: "15px",
    },
  };
  const [modalIsOpen, setIsOpen] = useState(false);
  const [modalIsOpen2, setModalIsOpen2] = useState(false);
  const [amountExch, setAmountExch] = useState(amount ? amount : "");
  const [amountExch1, setAmountExch1] = useState("");
  const [amountExchRecieve, setAmountExchRecieve] = useState("");
  const [amountExchRecieve1, setAmountExchRecieve1] = useState("");
  const [inputValidation, setInputValidation] = useState("");
  const [loading, setLoading] = useState(false);
  const [orderId, setOrderId] = useState(null);
  const [showOnce, setShowOnce] = useState(false);

  useEffect(() => {
    if (orderId) {
      const timeoutId = setTimeout(() => {
        router.push(`/pages/trade/${orderId}?type=user`);
      }, 700);

      return () => {
        clearTimeout(timeoutId);
      };
    }
  }, [orderId]);

  const handleTradeRequest = async () => {
    try {
      const payload = {
        listing_id: Number(listingId),
        trade_amount: Number(amountExch),
      };

      const response = await customFetchWithToken.post(
        "/trade/send-request/",
        payload
      );

      if (response.status === 200) {
        // toast.success(response.data.message);
        // Toast will be handled by WebSocket response - no duplicate needed
        const { data } = response.data;

        const queryParams = new URLSearchParams({
          type: "user",
        });

        setTimeout(() => {
          router.push(
            `/pages/trade/${data.order_id}?${queryParams.toString()}`
          );
        }, 1000);
      }
    } catch (error) {
      console.error("error", error);
      toast.error(error.response.data.error);
    }
  };

  const afterOpenModal = () => {
    // references are now sync'd and can be accessed.
  };

  const closeModal = () => {
    setIsOpen(false);
  };
  Modal.setAppElement("body");
  let subtitle;

  const handleSetAmountExch = (e) => {
    const value = e.target.value;

    if (value < 1 || value.length > 20) {
      setAmountExch("");
    } else if (value > max_liquidity) {
      setInputValidation("Value should be less than Maximum Liquidity");
      setAmountExch("");
    } else if (value < min_liquidity) {
      setInputValidation("Value should be more than Minimum Liquidity");
      setAmountExch(e.target.value);
    } else {
      setAmountExch(e.target.value);
      setInputValidation("");
    }
  };

  const createTradeRequest = () => {
    if (payOut_currency === "USDT") {
      try {
        handleTradeRequest();
      } catch (error) {
        console.log(error);
      }
    } else {
      setModalIsOpen2(true);
    }
  };

  const modalRateExch = async () => {
    if (amountExch.length < 1 && !modalIsOpen) {
      return null;
    }
    try {
      const res = await exchangeRateCurrencyApi(
        payIn_currency,
        payOut_currency,
        amountExch,
        listingId
      );

      setAmountExchRecieve(res.data.data.return_amount.toFixed(2));
    } catch (error) {
      console.error(error);
    }
  };

  const modalRateExch1 = async () => {
    if (amountExch.length < 1 && !modalIsOpen) {
      return null;
    }
    try {
      const res = await exchangeRateCurrencyApi1(
        payIn_currency,
        payOut_currency,
        amountExch1,
        listingId
      );

      setAmountExchRecieve1(res.data.data.return_amount);
    } catch (error) {
      console.error(error);
    }
  };

  const handleBuyFunc = () => {
    if (verifyStatus === "User_Detail") {
      router.push("/verification/personaldetails");
    } else if (verifyStatus === "Document-Verifications") {
      router.push("/verification/status");
    } else if (!token) {
      router.push("/");
    } else if (verifyStatus === "Dash_Board") {
      setIsOpen(!modalIsOpen);
    }
  };

  useEffect(() => {
    if (amountExch && amountExch.length > 0) {
      const timer = setTimeout(modalRateExch, 1000);
      return () => clearTimeout(timer);
    }
  }, [amountExch]);

  useEffect(() => {
    if (amountExch1.length > 0) {
      const timer = setTimeout(modalRateExch1, 1000);
      return () => clearTimeout(timer);
    }
  }, [amountExch1]);

  // Check if this listing should be highlighted
  const isHighlighted = highlightListingId && listingId && listingId.toString() === highlightListingId.toString();

  return (
    <>
      <tr className={`${styles.tableRow} ${isHighlighted ? styles.highlightedRow : ''}`}>
        {/* TRADER Column */}
        <td className={styles.tableCell}>
          <div className={styles.traderCell}>
            <div className={styles.traderInfo}>
              <div className={styles.traderHeader}>
                <div className={styles.onlineIndicator}></div>
                <span className={styles.traderName}>{name}</span>
              </div>
              <div className={styles.traderStats}>
                <span className={styles.completionRate}>100%</span>
                <span className={styles.tradeCount}>47 trades</span>
              </div>
            </div>
          </div>
        </td>

        {/* RATE Column */}
        <td className={styles.tableCell}>
          <div className={styles.rateCell}>
            <div className={styles.rateValue}>
              <span className={styles.rateAmount}>{rate}</span>
              <span className={styles.rateCurrency}>{payOut_currency}</span>
            </div>
            {/* <div className={styles.rateSubtext}>per USDT</div> */}
          </div>
        </td>

        {/* RANGE Column */}
        <td className={styles.tableCell}>
          <div className={styles.rangeCell}>
            <div className={styles.rangeValue}>
              {min_liquidity} - {max_liquidity}
            </div>
            <div className={styles.rangeCurrency}>{payIn_currency}</div>
          </div>
        </td>

        {/* LIQUIDITY Column */}
        <td className={styles.tableCell}>
          <div className={styles.liquidityCell}>
            <div className={styles.liquidityValue}>{available_liquidity}</div>
            <div className={styles.liquidityCurrency}>{payIn_currency}</div>
          </div>
        </td>

        {/* METHOD Column */}
        <td className={styles.tableCell}>
          <div className={styles.methodCell}>
            <div className={styles.paymentMethod}>
              <span className={styles.methodBadge}>
                {payOut_option || "UPI (India)"}
              </span>
            </div>
            <div className={styles.responseTime}>
              Response: {time_limit || 30} min
            </div>
          </div>
        </td>

        {/* ACTION Column */}
        <td className={styles.tableCell}>
          <div className={styles.actionCell}>
            <button
              className={styles.buyButton}
              onClick={handleBuyFunc}
              data-tooltip-id="my-tooltip12"
              data-tooltip-content="Click to trade"
            >
              Buy
            </button>
          </div>
        </td>
      </tr>

      {/* Modal remains the same */}
      <Modal
        isOpen={modalIsOpen}
        onAfterOpen={afterOpenModal}
        onRequestClose={closeModal}
        style={customStyles}
        contentLabel="Example Modal"
      >
        <div
          className={styles.modalCont}
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <div className={styles.modalTop}>
            <div className={styles.left}>
              <div className={styles.modalProfileWrapper}>
                <div className={styles.modalPic}>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    className="size-6"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.5 6a4.5 4.5 0 1 1 9 0 4.5 4.5 0 0 1-9 0ZM3.751 20.105a8.25 8.25 0 0 1 16.498 0 .75.75 0 0 1-.437.695A18.683 18.683 0 0 1 12 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 0 1-.437-.695Z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className={styles.modalName}>{name}</div>
                <div className={styles.orders}> 47 Orders</div>
                <div className={styles.orders}>100% Completion </div>

                <div className={styles.orders}>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="9"
                    height="8"
                    viewBox="0 0 9 8"
                    fill="none"
                  >
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M5.85286 0.673541C5.64694 0.623275 5.42971 0.690988 5.29161 0.848453L3.04356 3.41181V7.34283H6.90327C7.17038 7.34283 7.40034 7.15858 7.45274 6.90256L7.99065 4.27388C7.99065 4.27388 7.99065 4.27389 7.99065 4.27388C8.05999 3.935 7.79479 3.61884 7.44119 3.61884H5.39693C5.27295 3.61884 5.15902 3.55214 5.10052 3.4453C5.04201 3.33845 5.04843 3.20881 5.1172 3.10798L6.20459 1.51386C6.41951 1.19874 6.25016 0.770626 5.87424 0.678773L5.85286 0.673541C5.85289 0.673549 5.85282 0.673532 5.85286 0.673541ZM2.37117 7.34283V3.61885L1.25052 3.61884V7.34283H2.37117ZM2.55274 2.96167L4.78109 0.420772C5.08496 0.0742817 5.56302 -0.0746649 6.01602 0.0360107L6.03732 0.0412219C6.03729 0.0412141 6.03735 0.0412296 6.03732 0.0412219C6.86429 0.24335 7.23685 1.18517 6.76407 1.87837L6.02513 2.96167H7.44119C8.21909 2.96167 8.80253 3.65722 8.64999 4.40276L8.11207 7.03145C7.99681 7.59463 7.49091 8 6.90327 8H0.914321C0.728645 8 0.578125 7.85289 0.578125 7.67141V3.29025C0.578125 3.20311 0.613546 3.11953 0.676595 3.05791C0.739644 2.99628 0.825158 2.96167 0.914323 2.96167L2.55274 2.96167Z"
                      fill="#7939EE"
                    />
                  </svg>
                  95.76%
                </div>
              </div>
            </div>
            <div className={styles.right}>
              <svg
                onClick={closeModal}
                style={{ cursor: "pointer" }}
                xmlns="http://www.w3.org/2000/svg"
                width="15"
                height="15"
                viewBox="0 0 15 15"
                fill="none"
              >
                <path
                  d="M8.60216 7.483L14.8197 1.28213C14.9425 1.13904 15.0067 0.954975 14.9994 0.766723C14.9921 0.57847 14.9139 0.399895 14.7803 0.266681C14.6468 0.133468 14.4677 0.0554274 14.2789 0.048156C14.0902 0.0408846 13.9056 0.104918 13.7622 0.227459L7.54466 6.42833L1.32716 0.219979C1.18593 0.0791288 0.994384 0 0.794658 0C0.594931 0 0.403385 0.0791288 0.262158 0.219979C0.12093 0.360829 0.0415888 0.551863 0.0415888 0.751055C0.0415888 0.950247 0.12093 1.14128 0.262158 1.28213L6.48716 7.483L0.262158 13.6839C0.183646 13.7509 0.119881 13.8334 0.0748636 13.9262C0.0298461 14.019 0.00454851 14.1201 0.000558893 14.2231C-0.00343073 14.3261 0.0139734 14.4289 0.0516793 14.5249C0.0893852 14.6208 0.146579 14.708 0.21967 14.7809C0.292761 14.8538 0.380171 14.9109 0.476415 14.9485C0.572659 14.9861 0.675658 15.0034 0.778948 14.9994C0.882237 14.9955 0.983586 14.9702 1.07664 14.9253C1.16968 14.8804 1.25242 14.8168 1.31966 14.7385L7.54466 8.53767L13.7622 14.7385C13.9056 14.8611 14.0902 14.9251 14.2789 14.9178C14.4677 14.9106 14.6468 14.8325 14.7803 14.6993C14.9139 14.5661 14.9921 14.3875 14.9994 14.1993C15.0067 14.011 14.9425 13.827 14.8197 13.6839L8.60216 7.483Z"
                  fill="#858585"
                />
              </svg>
            </div>
          </div>
          <div className={styles.modalBottom}>
            <div className={styles.leftB}>
              <div className={styles.info}>
                <div className={styles.infoPoints}>
                  Rate : <span className={styles.highlight}>{rate}</span>
                </div>
                <div className={styles.infoPoints}>
                  Available Trade:
                  <span className={styles.highlight}>
                    {available_liquidity}
                  </span>
                </div>
                <div className={styles.infoPoints}>
                  Minimum Trade:
                  <span className={styles.highlight}>{min_liquidity}</span>
                </div>
                <div className={styles.infoPoints}>
                  Maximum Trade :{" "}
                  <span className={styles.highlight}>{max_liquidity}</span>
                </div>
                <div className={styles.infoPoints}>
                  Listed on :{" "}
                  <span className={styles.highlight}>{datePart}</span>
                </div>
                <div className={styles.infoPoints}>
                  Payment time limit :{" "}
                  <span className={styles.highlight}>{time_limit} minutes</span>
                </div>
                <div className={styles.infoPoints}>
                  Seller payIn Method :
                  <span style={{ marginLeft: "10px" }}>{payIn_option}</span>
                </div>
                <div className={styles.infoPoints}>
                  Seller payOut Method :
                  <span style={{ marginLeft: "10px" }}>{payOut_option}</span>
                </div>
              </div>
              <div className={styles.terms}>
                <div className={styles.tHeader}>Term & Conditions : </div>
                <div className={styles.termsPara}>{terms}</div>
              </div>
            </div>
            <div className={styles.rightB}>
              <div>
                <label htmlFor="">I want to pay </label>
                <div className={styles.validationCheck}>
                  {inputValidation ? inputValidation : ""}
                </div>
                <div className={styles.payInput}>
                  <input
                    type="number"
                    placeholder={`${min_liquidity || 0} - ${
                      max_liquidity || 0
                    }`}
                    onChange={handleSetAmountExch}
                    value={amountExch ? amountExch : amountExchRecieve1}
                    required
                    min={min_liquidity}
                    max={max_liquidity}
                  />

                  {payIn_currency}
                </div>
              </div>
              <div>
                <label htmlFor="">I will receive</label>
                <div className={styles.payInput}>
                  <div className={styles.receive}>
                    <input
                      type="number"
                      placeholder={`${min_liquidity * rate || 0} - ${
                        max_liquidity * rate || 0
                      }`}
                      onChange={(e) => {
                        setAmountExch1(e.target.value);
                      }}
                      value={
                        amountExchRecieve ? amountExchRecieve : amountExch1
                      }
                      required
                    />
                  </div>
                  {payOut_currency}
                </div>
              </div>
              <div className={styles.buttonBBUY} onClick={createTradeRequest}>
                Buy {payOut_currency}
              </div>
              {modalIsOpen2 ? (
                <RecipientConfirmModal
                  payOut_currency={payOut_currency}
                  modalIsOpen2={modalIsOpen2}
                  setModalIsOpen2={setModalIsOpen2}
                  enteredAmount={amountExch}
                  payOut_option={payOut_option}
                  listingId={listingId}
                  name={name}
                  min_liquidity={min_liquidity}
                  max_liquidity={max_liquidity}
                  available_liquidity={available_liquidity}
                  payIn_option={payIn_option}
                  rate={rate}
                  terms={terms}
                  time={time}
                  payIn_currency={payIn_currency}
                />
              ) : (
                ""
              )}
            </div>
          </div>
        </div>
      </Modal>
      <Tooltip
        id="my-tooltip12"
        style={{
          color: "#fff",
          fontSize: "12px",
        }}
      />
    </>
  );
};

export default page;
