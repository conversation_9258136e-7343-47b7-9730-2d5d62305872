"use client";
import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { useSearchParams, useParams } from "next/navigation";
import { toast, ToastContainer } from "react-toastify";
import { CopyToClipboard } from "react-copy-to-clipboard";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import styles from "../search_results.module.css";
import Modal from "react-modal";
import Chat from "../../../components/Chat/page";
import Login from "@/app/sign/login/page";
import "react-toastify/dist/ReactToastify.css";
import TradeTimer from "../../../components/TradeTimer/page";
import TradeStepper from "../../../components/TradeStepper/page";
import DisputeTicket from "../../../components/TradepageModals/DisputeTicket/page";
import CountDownTimer from "../../../components/CountDownTimer/page";
import ReportTrade from "../../../components/ReportTrade/page";
import TradeReviewModal from "../../../components/TradeReviewModal/page";
import { useSSE } from "@/app/context/SSEContext";
import Layout from "../../../components/Layout/page";
const page = () => {
  const useQueryParams = useSearchParams();
  const router = useRouter();
  const { event, remflowEvent } = useSSE();

  const authTokenRef = useRef(null);

  const params = useParams();
  const [tradeData, setTradeData] = useState("");
  const [timeLimit, setTimeLimit] = useState("");
  const [btnNameUser, setBtnNameUser] = useState("");
  const [btnNamePeer, setBtnNamePeer] = useState("");
  const [userTimeLine, setUserTimeLine] = useState("");
  const [peerTimeLine, setPeerTimeLine] = useState("");
  const [tradeDecision, setTradeDecision] = useState("");
  const [payoutDetails, setPayoutDetails] = useState();
  const [activeStep, setActiveStep] = useState(0);

  console.log("tradeData", tradeData);
  console.log("tradeData?.flag", tradeData?.flag);
  console.log("tradeData?.data", tradeData?.data);
  console.log("activeStep123", activeStep);

  const orderNumber = params.id;
  const passedType = useQueryParams.get("type");
  console.log("increaseTimer", remflowEvent);

  useEffect(() => {
    // Only process remflowEvent if it has meaningful data
    if (!remflowEvent || !remflowEvent.flag) return;

    if (remflowEvent.flag === "user") {
      setActiveStep(Number(remflowEvent.stepper));
      setBtnNameUser(remflowEvent.button);
      setUserTimeLine(remflowEvent.time_line_state);
      // Toast notifications are handled in SSEContext
    } else if (remflowEvent.flag === "peer") {
      setActiveStep(Number(remflowEvent.stepper));
      setBtnNamePeer(remflowEvent.button);
      setPeerTimeLine(remflowEvent.time_line_state);
      // Toast notifications are handled in SSEContext
    }
  }, [remflowEvent]);

  const getPeerDetailsByOrderId = async () => {
    try {
      const res = await customFetchWithToken.get(
        `/trade/history/peer/data/?order_id=${orderNumber}`
      );
      console.log("resqq", res);
      setPeerTimeLine(res.data.data.time_line_state);
      setBtnNamePeer(res.data.data.button);
      setActiveStep(Number(res.data.data.stepper));
      // Messages will come via SSE - no duplicate toast needed
    } catch (error) {
      console.error("resqq", error);
    }
  };
  const getUserDetailsByOrderId = async () => {
    try {
      const res = await customFetchWithToken.get(
        `/trade/history/user/data/?order_id=${orderNumber}`
      );
      console.log("resqqq", res);
      setBtnNameUser(res.data.data.button);
      setUserTimeLine(res.data.data.time_line_state);
      setActiveStep(Number(res.data.data.stepper));
      // Messages will come via SSE - no duplicate toast needed
    } catch (error) {
      console.error("resqqqq", error);
    }
  };

  useEffect(() => {
    if (tradeData?.flag !== "user") {
      getPeerDetailsByOrderId();
    } else if (tradeData?.flag === "user") {
      getUserDetailsByOrderId();
    }
  }, [tradeData?.flag]);

  let username1;
  if (typeof window !== "undefined") {
    username1 = localStorage.getItem("userName");
  }

  // Note: Removed fetchPeerDetailsApi as it was calling undefined state setters

  const [modalIsOpen, setIsOpen] = useState(false);
  const [modalIsOpen2, setIsOpen2] = useState(false);
  const [modalIsOpen3, setIsOpen3] = useState(false);
  const [modalIsOpen4, setIsOpen4] = useState(false);
  const [modalIsOpen5, setIsOpen5] = useState(false);
  const [showReviewModal, setShowReviewModal] = useState(false);

  const openModal = () => {
    setIsOpen(!modalIsOpen);
  };
  const openModal2 = () => {
    setIsOpen2(!modalIsOpen2);
    setIsOpen(false);
  };
  const openModal3 = () => {
    setIsOpen3(!modalIsOpen3);
  };
  const openModal4 = () => {
    setIsOpen4(!modalIsOpen4);
  };
  const openModal5 = () => {
    setIsOpen5(!modalIsOpen4);
  };
  // const openModal4 = () => {
  //   setIsOpen3(!modalIsOpen3);
  //   setIsOpen2(false);
  //   setIsOpen(false);
  // };

  const afterOpenModal = () => {
    // references are now sync'd and can be accessed.
  };
  const afterOpenModal2 = () => {
    // references are now sync'd and can be accessed.
  };
  const afterOpenModal3 = () => {
    // references are now sync'd and can be accessed.
  };
  const afterOpenModal4 = () => {
    // references are now sync'd and can be accessed.
  };

  const closeModal = () => {
    setIsOpen(false);
  };
  const closeModal2 = () => {
    setIsOpen(false);
  };
  const closeModal3 = () => {
    setIsOpen3(false);
  };
  const closeModal4 = () => {
    setIsOpen3(false);
  };

  const handleCopyName = () => {
    toast.success("Name copied to clipboard!", {
      position: "top-right",
      autoClose: 2000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
    });
  };

  const handleCopyPaymentMethod = () => {
    toast.success("Payment method copied to clipboard!", {
      position: "top-right",
      autoClose: 2000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
    });
  };

  const handleCopyPaymentDetails = () => {
    toast.success("Payment details copied to clipboard!", {
      position: "top-right",
      autoClose: 2000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
    });
  };

  // Helper function to get payment method text
  const getPaymentMethodText = () => {
    return tradeData?.data?.payin_option || "No payment method available";
  };

  // Helper function to get payment details text
  const getPaymentDetailsText = () => {
    if (Array.isArray(tradeData?.data?.payin_data)) {
      return tradeData.data.payin_data
        .map((item) => `${item.key}: ${item.value}`)
        .join("\n");
    } else if (typeof tradeData?.data?.payin_data === "string") {
      return tradeData.data.payin_data;
    } else if (tradeData?.data?.payin_data) {
      return JSON.stringify(tradeData.data.payin_data, null, 2);
    } else {
      return "No payment details available";
    }
  };

  const getOrderDetails = async () => {
    try {
      const res = await customFetchWithToken.get(
        `/trade/order/details/?order_id=${orderNumber}`
      );
      console.log("Trade order details:", res.data);

      if (res.data?.data) {
        const responseData = res.data.data;

        // Set time limit data
        if (responseData.time_limit) {
          setTimeLimit(responseData.time_limit);
        }

        // Set order data
        if (responseData.order_data) {
          const orderData = responseData.order_data;
          setTradeData({
            currency_from: orderData.currency_from,
            currency_to: orderData.currency_to,
            trade_amount: orderData.trade_amount,
            listing_data: orderData.listing_data,
            user_details: orderData.user_details,
            peer_details: orderData.peer_details,
            data: orderData.data,
            flag: orderData.flag,
            order_status: orderData.order_status,
          });

          // Set stepper to 5 if order status is completed
          if (orderData.order_status === "completed") {
            setActiveStep(5);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching trade details:", error);
    }
  };

  useEffect(() => {
    getOrderDetails();
  }, [remflowEvent]);

  // Note: Removed unused FormData objects that were being created on every render

  const handleCancelTrade = async () => {
    const payload = {
      order_id: orderNumber,
    };
    try {
      const res = await customFetchWithToken.post(
        "/trade/cancel-order/",
        payload
      );

      if (res.status === 200) {
        toast.success(res.data.message);
        // router.push("/pages/searchads");
      }
    } catch (error) {
      console.error("error", error);
    }
    // setTimeout(() => {
    //   router.push("/pages/searchads");
    // }, 1000);
  };
  const handleCancelTradePeer = async () => {
    const payload = {
      order_id: orderNumber,
    };
    try {
      const res = await customFetchWithToken.post(
        "/trade/cancel-order/",
        payload
      );
      console.log("res", res);
      if (res.status === 200) {
        toast.success(res.data.message);
        // router.push("/pages/searchads");
      }
    } catch (error) {
      console.error("error", error);
    }
    // setTimeout(() => {
    //   router.push("/pages/searchads");
    // }, 1000);
  };

  const paymentRecivedByPeerApi = async () => {
    const payload = {
      order_id: orderNumber,
    };

    try {
      const res = await customFetchWithToken.post(
        "/trade/received-payment-by-peer/",
        payload
      );
      console.log("res", res);
      if (res.status === 200 && res.data.data.flag === "peer") {
        // toast.success(res.data.message);
        setBtnNamePeer(res.data.data.button);
        setPeerTimeLine(res.data.data.time_line_state);
        setActiveStep(Number(res.data.data.stepper));
      }
    } catch (error) {
      console.error("error", error);
    }
  };

  const sendMoneyToUserApi = async () => {
    const payload = {
      order_id: orderNumber,
    };

    try {
      const res = await customFetchWithToken.post(
        "/trade/send-money-to-user/",
        payload
      );

      if (res.status === 200 && res.data.data.flag === "peer") {
        // toast.success(res.data.message);
        setBtnNamePeer(res.data.data.button);
        setPeerTimeLine(res.data.data.time_line_state);
        setActiveStep(Number(res.data.data.stepper));
      }
    } catch (error) {
      console.error("error", error);
    }
  };

  const sendPaymentFromPeerToSender = async () => {
    if (peerTimeLine === "received_payment_by_peer") {
      paymentRecivedByPeerApi();
    }
    if (peerTimeLine === "send_to_user") {
      sendMoneyToUserApi();
    }

    if (peerTimeLine === "trade_complete") {
      setShowReviewModal(true);
    }
  };
  useEffect(() => {
    if (peerTimeLine === "trade_complete") {
      setShowReviewModal(true);
    }
  }, [peerTimeLine]);

  const sendMoneyToPeerApi = async () => {
    const payload = { order_id: orderNumber };
    try {
      const res = await customFetchWithToken.post(
        "/trade/send-money-to-peer/",
        payload
      );
      console.log("res", res);
      if (res.status === 200 && res.data.data.flag === "user") {
        // toast.success(res.data.message);
        setBtnNameUser(res.data.data.button);
        setUserTimeLine(res.data.data.time_line_state);
        setActiveStep(Number(res.data.data.stepper));
      }
    } catch (error) {
      console.error("error", error);
    }
  };
  const paymentRecivedByUserApi = async () => {
    const payload = { order_id: orderNumber };
    try {
      const res = await customFetchWithToken.post(
        "/trade/received-payment-by-user/",
        payload
      );
      console.log("res", res);
      if (res.status === 200 && res.data.data.flag === "user") {
        // toast.success(res.data.message);
        setBtnNameUser(res.data.data.button);
        setUserTimeLine(res.data.data.time_line_state);
        setActiveStep(Number(res.data.data.stepper));
        setTimeout(() => {
          setShowReviewModal(true);
        }, 1000);
      }
    } catch (error) {
      console.error("error", error);
    }
  };

  const sendPaymentFromSenderToPeer = async () => {
    if (userTimeLine === "pay_to_peer") {
      sendMoneyToPeerApi();
    }

    if (userTimeLine === "received_payment_by_user") {
      paymentRecivedByUserApi();
    }
    if (userTimeLine === "Trade_Completed") {
      setShowReviewModal(true);
    }
  };

  let token;
  if (typeof window !== "undefined") {
    token = sessionStorage.getItem("user");
  }

  if (!token && typeof window !== "undefined") {
    router.push("/sign/login");
  }

  const customStyles = {
    content: {
      top: "50%",
      left: "50%",
      right: "auto",
      bottom: "auto",
      marginRight: "-50%",
      transform: "translate(-50%, -50%)",
      borderRadius: "15px",
      width: "400px",
      zIndex: 100000,
    },
    overlay: {
      zIndex: 99999,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
    },
  };
  if (typeof window !== "undefined") {
    Modal.setAppElement("body");
  }
  let subtitle;

  // Create title for Layout component
  const tradeTitle = (
    <div className={styles.headerContent}>
      <h1 className={styles.pageTitle}>Trade</h1>
      <p className={styles.pageSubtitle}>
        Execute your trade securely • Order #{orderNumber}
      </p>
    </div>
  );

  return (
    <>
      <Layout title={tradeTitle}>
        <div className={styles.rightContainer}>
          <div className={styles.rightContainerWrapper}>
            <TradeTimer
              duration={timeLimit?.left_time_in_milliseconds}
              orderNumber={orderNumber}
              showModal={true}
              onTimeIncreased={getOrderDetails}
            />
            <div className={styles.topBoxWrapper}>
              <div className={styles.topLeftBox}>
                <div className={styles.headerBtn}>
                  <span className={styles.HeaderBuyBtn}>
                    {tradeData?.flag === "user" ? "BUY" : "SELL"}
                  </span>{" "}
                  {tradeData?.currency_to}{" "}
                  {tradeData?.flag === "user" ? "with" : "for"}{" "}
                  {tradeData?.currency_from}{" "}
                  {tradeData?.flag === "user" ? "from" : "to"}{" "}
                  {tradeData?.flag === "user"
                    ? tradeData?.peer_details?.username
                    : tradeData?.user_details?.username}
                </div>
                <div className={styles.tradeStatusContainer}>
                  <div className={styles.timeSubHeader}>
                    <span className={styles.statusLabel}>Trade Status:</span>
                    <span className={styles.statusValue}>
                      {timeLimit?.status || "active"}
                    </span>
                    <CountDownTimer
                      duration={timeLimit?.left_time_in_milliseconds}
                      tradeStatus={timeLimit?.status}
                      className={styles.countDownTimerInline}
                    />
                  </div>
                </div>
              </div>

              <div className={styles.topMiddleBox}>
                <div className={styles.rightheaderinfo}>
                  <div className={styles.rinfo}>
                    Order number - {orderNumber}
                  </div>
                  <div className={styles.rinfo}>
                    Time created -{" "}
                    {timeLimit?.created_date
                      ? new Date(timeLimit.created_date).toLocaleString()
                      : "N/A"}
                  </div>
                  <div className={styles.rinfo}>
                    Time limit - {tradeData?.listing_data?.time_limit} minutes
                  </div>
                  {timeLimit?.expiry_date && (
                    <div className={styles.rinfo}>
                      Expires at -{" "}
                      {new Date(timeLimit.expiry_date).toLocaleString()}
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className={styles.bottomBoxWrapper}>
              <div className={styles.bottomLeftBox}>
                <div className={styles.tagArea}>
                  <div className={styles.senderTag}>
                    {tradeData?.flag === "user" || passedType === "user"
                      ? "You are the Sender"
                      : "You are the Peer"}
                  </div>
                  {(timeLimit?.status === "completed" || showReviewModal) && (
                    <div className={styles.completedTag}>Trade Completed</div>
                  )}
                  {timeLimit?.status === "expired" && (
                    <div className={styles.expiredTag}>Trade Expired</div>
                  )}
                </div>
                {timeLimit?.status === "completed" ||
                timeLimit?.status === "expired" ? (
                  ""
                ) : (
                  <div className={styles.nextStepArea}>
                    {activeStep === 1 ? (
                      <span>
                        {tradeData?.flag === "user" ? (
                          <div className={styles.dialogueSteps}>
                            Click on the Pay to peer Button to confirm payment
                            to peer
                          </div>
                        ) : (
                          <div className={styles.dialogueSteps}>
                            Please wait for the user to send payment to peer
                          </div>
                        )}
                      </span>
                    ) : activeStep === 2 ? (
                      <span>
                        {tradeData?.flag === "user" ? (
                          <div className={styles.dialogueSteps}>
                            Wait for the Peer to mark the payment as Received
                          </div>
                        ) : (
                          <div className={styles.dialogueSteps}>
                            Click on the Payment Received button to confirm
                            payment received from the user
                          </div>
                        )}
                      </span>
                    ) : activeStep === 3 ? (
                      <span>
                        {tradeData?.flag === "user" ? (
                          <div className={styles.dialogueSteps}>
                            Wait for the Peer to Send the payment to you
                          </div>
                        ) : (
                          <div className={styles.dialogueSteps}>
                            Click on the Paid to User button to after sending
                            the payment to the sender
                          </div>
                        )}
                      </span>
                    ) : activeStep === 4 ? (
                      <span>
                        {tradeData?.flag === "user" ? (
                          <div className={styles.dialogueSteps}>
                            Click on the Payment received button to confirm
                            payment received from the peer
                          </div>
                        ) : (
                          <div className={styles.dialogueSteps}>
                            Wait for the user to confirm payment received from
                            you
                          </div>
                        )}
                      </span>
                    ) : activeStep === 5 ? (
                      <span>
                        {tradeData?.flag === "user" ? (
                          <div className={styles.dialogueSteps}>
                            Trade Completed
                          </div>
                        ) : (
                          <div className={styles.dialogueSteps}>
                            Trade Completed
                          </div>
                        )}
                      </span>
                    ) : activeStep === 6 ? (
                      <span>
                        {tradeData?.flag === "user" ? (
                          <div className={styles.dialogueSteps}>
                            Click on the Payment received button to confirm
                            payment received from the peer
                          </div>
                        ) : (
                          <div className={styles.dialogueSteps}>
                            Wait for the user to confirm payment received from
                            you
                          </div>
                        )}
                      </span>
                    ) : activeStep === 7 ? (
                      <span>
                        {tradeData?.flag === "user" ? (
                          <div className={styles.dialogueSteps}>
                            Click on the Trade Completed button to mark the
                            trade as completed
                          </div>
                        ) : (
                          <div className={styles.dialogueSteps}>
                            Click on the Trade Completed button to mark the
                            trade as completed
                          </div>
                        )}
                      </span>
                    ) : (
                      ""
                    )}
                  </div>
                )}
                <div className={styles.progressBarArea}>
                  {/* <ProgressBar /> */}
                  <TradeStepper activeStep={activeStep} />
                </div>
                <div className={styles.confirmOrderInfoArea}>
                  {/* <div className={styles.orderConfirmHeader}>
                        Confirm Order Info
                      </div> */}
                  <div className={styles.orderConfirmInfo}>
                    <div className={styles.orderConfirmInfoSingle}>
                      Currency Pair : {tradeData?.currency_from} -{" "}
                      {tradeData?.currency_to}|
                    </div>
                    <div className={styles.orderConfirmInfoSingle}>
                      Rate :{" "}
                      {tradeData?.listing_data?.final_trade_fee?.toFixed(2)} |
                    </div>
                    <div className={styles.orderConfirmInfoSingle}>
                      Amount : {tradeData?.trade_amount} |
                    </div>
                    <div className={styles.orderConfirmInfoSingle}>
                      Limit : {tradeData?.listing_data?.min_liquidity} -{" "}
                      {tradeData?.listing_data?.max_liquidity} |
                    </div>
                  </div>
                  <div className={styles.orderDialogue}>
                    Please make payment to this account to fund your trade with
                    this peer.
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="12"
                      viewBox="0 0 14 12"
                      fill="none"
                    >
                      <path
                        d="M7.01228 11.25C5.61342 11.25 4.27185 10.6969 3.2827 9.71231C2.29356 8.72774 1.73786 7.39239 1.73786 6C1.73786 4.60761 2.29356 3.27226 3.2827 2.28769C4.27185 1.30312 5.61342 0.75 7.01228 0.75C8.41114 0.75 9.75271 1.30312 10.7419 2.28769C11.731 3.27226 12.2867 4.60761 12.2867 6C12.2867 7.39239 11.731 8.72774 10.7419 9.71231C9.75271 10.6969 8.41114 11.25 7.01228 11.25ZM7.01228 12C8.61098 12 10.1442 11.3679 11.2747 10.2426C12.4051 9.11742 13.0402 7.5913 13.0402 6C13.0402 4.4087 12.4051 2.88258 11.2747 1.75736C10.1442 0.632141 8.61098 0 7.01228 0C5.41358 0 3.88036 0.632141 2.74991 1.75736C1.61946 2.88258 0.984375 4.4087 0.984375 6C0.984375 7.5913 1.61946 9.11742 2.74991 10.2426C3.88036 11.3679 5.41358 12 7.01228 12Z"
                        fill="#7939EE"
                      />
                      <path
                        d="M7.71375 4.941L5.98826 5.15625L5.92648 5.44125L6.26555 5.5035C6.48707 5.556 6.53078 5.6355 6.48255 5.85525L5.92648 8.45625C5.7803 9.129 6.0056 9.4455 6.5353 9.4455C6.94595 9.4455 7.42291 9.2565 7.63916 8.997L7.70546 8.685C7.55477 8.817 7.33475 8.8695 7.18857 8.8695C6.98136 8.8695 6.90601 8.72475 6.95951 8.46975L7.71375 4.941ZM7.7665 3.375C7.7665 3.57391 7.68711 3.76468 7.54581 3.90533C7.4045 4.04598 7.21285 4.125 7.01301 4.125C6.81317 4.125 6.62152 4.04598 6.48021 3.90533C6.33891 3.76468 6.25952 3.57391 6.25952 3.375C6.25952 3.17609 6.33891 2.98532 6.48021 2.84467C6.62152 2.70402 6.81317 2.625 7.01301 2.625C7.21285 2.625 7.4045 2.70402 7.54581 2.84467C7.68711 2.98532 7.7665 3.17609 7.7665 3.375Z"
                        fill="#7939EE"
                      />
                    </svg>
                  </div>
                  <div className={styles.paymentInfoContainer}>
                    <div className={styles.paymentMethodDisplay}>
                      <div className={styles.paymentMethod}>
                        {/* {tradeData?.flag === "user"
                          ? tradeData?.data?.payin_option
                          : tradeData?.data?.payout_option} */}
                        {tradeData?.data?.payin_option}
                      </div>
                      <div className={styles.passedTerms}>
                        {tradeData?.listing_data?.terms_and_conditions}
                      </div>
                    </div>

                    <div className={styles.paymentAddressDisplay}>
                      <div className={styles.paymentAddressName}>
                        <div>
                          Name-{" "}
                          {tradeData?.flag === "user" ? (
                            <span>
                              {tradeData?.peer_details?.firstname}{" "}
                              {tradeData?.peer_details?.lastname}
                            </span>
                          ) : (
                            <span>
                              {tradeData?.user_details?.firstname}{" "}
                              {tradeData?.user_details?.lastname}
                            </span>
                          )}
                        </div>
                        <div>
                          <CopyToClipboard
                            text={
                              tradeData?.flag === "user"
                                ? `${tradeData?.peer_details?.firstname} ${tradeData?.peer_details?.lastname}`.trim()
                                : `${tradeData?.user_details?.firstname} ${tradeData?.user_details?.lastname}`.trim()
                            }
                            onCopy={handleCopyName}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="11"
                              height="12"
                              viewBox="0 0 11 12"
                              fill="none"
                              style={{ cursor: "pointer" }}
                              title="Copy name to clipboard"
                            >
                              <path
                                d="M8.91253 10.3815H8.04721V11.1907H0.908292V2.89614H1.77362V2.08691H0.0429688V12H8.91253V10.3815Z"
                                fill="#A5A5A5"
                              />
                              <path
                                d="M2.12891 0V9.91304H10.9985V3.54631L7.19085 0H2.12891ZM10.1115 9.08696H3.01586V0.826087H5.89847V4.75H10.1115V9.08696ZM10.1115 3.92391H6.78543V0.826087H6.82346L10.1115 3.88847V3.92391Z"
                                fill="#A5A5A5"
                              />
                            </svg>
                          </CopyToClipboard>
                        </div>
                      </div>
                      <div className={styles.paymentAddressName}>
                        <div>
                          Payment Method - {tradeData?.data?.payin_option}
                        </div>

                        <div>
                          <CopyToClipboard
                            text={getPaymentMethodText()}
                            onCopy={handleCopyPaymentMethod}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="11"
                              height="12"
                              viewBox="0 0 11 12"
                              fill="none"
                              style={{ cursor: "pointer" }}
                              title="Copy payment method to clipboard"
                            >
                              <path
                                d="M8.91253 10.3815H8.04721V11.1907H0.908292V2.89614H1.77362V2.08691H0.0429688V12H8.91253V10.3815Z"
                                fill="#A5A5A5"
                              />
                              <path
                                d="M2.12891 0V9.91304H10.9985V3.54631L7.19085 0H2.12891ZM10.1115 9.08696H3.01586V0.826087H5.89847V4.75H10.1115V9.08696ZM10.1115 3.92391H6.78543V0.826087H6.82346L10.1115 3.88847V3.92391Z"
                                fill="#A5A5A5"
                              />
                            </svg>
                          </CopyToClipboard>
                        </div>
                      </div>
                      <div className={styles.paymentAddressName}>
                        <div>
                          Payment Details -{" "}
                          {Array.isArray(tradeData?.data?.payin_data) ? (
                            <div className={styles.tradePayDetails}>
                              {tradeData.data.payin_data.map((item, index) => (
                                <div
                                  key={index}
                                  className={styles.paymentDetailItem}
                                >
                                  <strong>{item.key}:</strong> {item.value}
                                </div>
                              ))}
                            </div>
                          ) : (
                            <div className={styles.paymentDetailItem}>
                              {typeof tradeData?.data?.payin_data === "string"
                                ? tradeData.data.payin_data
                                : tradeData?.data?.payin_data
                                ? JSON.stringify(tradeData.data.payin_data)
                                : "No payin details available"}
                            </div>
                          )}
                        </div>
                        <div>
                          <CopyToClipboard
                            text={getPaymentDetailsText()}
                            onCopy={handleCopyPaymentDetails}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="11"
                              height="12"
                              viewBox="0 0 11 12"
                              fill="none"
                              style={{ cursor: "pointer" }}
                              title="Copy payment details to clipboard"
                            >
                              <path
                                d="M8.91253 10.3815H8.04721V11.1907H0.908292V2.89614H1.77362V2.08691H0.0429688V12H8.91253V10.3815Z"
                                fill="#A5A5A5"
                              />
                              <path
                                d="M2.12891 0V9.91304H10.9985V3.54631L7.19085 0H2.12891ZM10.1115 9.08696H3.01586V0.826087H5.89847V4.75H10.1115V9.08696ZM10.1115 3.92391H6.78543V0.826087H6.82346L10.1115 3.88847V3.92391Z"
                                fill="#A5A5A5"
                              />
                            </svg>
                          </CopyToClipboard>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className={styles.uploadDialogue}>
                    <svg
                      className={styles.ctaDialogue}
                      style={{ marginLeft: "5px" }}
                      xmlns="http://www.w3.org/2000/svg"
                      width="13"
                      height="12"
                      viewBox="0 0 13 12"
                      fill="none"
                    >
                      <path
                        d="M6.02791 11.25C4.62904 11.25 3.28748 10.6969 2.29833 9.71231C1.30918 8.72774 0.753488 7.39239 0.753488 6C0.753488 4.60761 1.30918 3.27226 2.29833 2.28769C3.28748 1.30312 4.62904 0.75 6.02791 0.75C7.42677 0.75 8.76834 1.30312 9.75748 2.28769C10.7466 3.27226 11.3023 4.60761 11.3023 6C11.3023 7.39239 10.7466 8.72774 9.75748 9.71231C8.76834 10.6969 7.42677 11.25 6.02791 11.25ZM6.02791 12C7.62661 12 9.15983 11.3679 10.2903 10.2426C11.4207 9.11742 12.0558 7.5913 12.0558 6C12.0558 4.4087 11.4207 2.88258 10.2903 1.75736C9.15983 0.632141 7.62661 0 6.02791 0C4.42921 0 2.89598 0.632141 1.76553 1.75736C0.635081 2.88258 0 4.4087 0 6C0 7.5913 0.635081 9.11742 1.76553 10.2426C2.89598 11.3679 4.42921 12 6.02791 12Z"
                        fill="#7939EE"
                      />
                      <path
                        d="M6.72938 4.941L5.00389 5.15625L4.9421 5.44125L5.28117 5.5035C5.5027 5.556 5.5464 5.6355 5.49818 5.85525L4.9421 8.45625C4.79593 9.129 5.02122 9.4455 5.55092 9.4455C5.96157 9.4455 6.43853 9.2565 6.65478 8.997L6.72109 8.685C6.57039 8.817 6.35037 8.8695 6.2042 8.8695C5.99699 8.8695 5.92164 8.72475 5.97514 8.46975L6.72938 4.941ZM6.78212 3.375C6.78212 3.57391 6.70274 3.76468 6.56143 3.90533C6.42012 4.04598 6.22847 4.125 6.02863 4.125C5.8288 4.125 5.63714 4.04598 5.49584 3.90533C5.35453 3.76468 5.27515 3.57391 5.27515 3.375C5.27515 3.17609 5.35453 2.98532 5.49584 2.84467C5.63714 2.70402 5.8288 2.625 6.02863 2.625C6.22847 2.625 6.42012 2.70402 6.56143 2.84467C6.70274 2.98532 6.78212 3.17609 6.78212 3.375Z"
                        fill="#7939EE"
                      />
                    </svg>{" "}
                    Please upload a receipt image in the chat to confirm payment
                    has been sent.
                  </div>
                </div>
                <div className={styles.activityBtnArea}>
                  {tradeData?.flag !== "user" ? (
                    <div className={styles.leftBtns}>
                      <div
                        className={`${styles.notifySellerBtn} ${
                          timeLimit?.status === "completed" ||
                          timeLimit?.status === "expired"
                            ? styles.disabledBtn
                            : ""
                        }`}
                        onClick={
                          timeLimit?.status === "completed" ||
                          timeLimit?.status === "expired"
                            ? null
                            : sendPaymentFromPeerToSender
                        }
                        style={{
                          opacity:
                            timeLimit?.status === "completed" ||
                            timeLimit?.status === "expired"
                              ? 0.5
                              : 1,
                          cursor:
                            timeLimit?.status === "completed" ||
                            timeLimit?.status === "expired"
                              ? "not-allowed"
                              : "pointer",
                        }}
                      >
                        {timeLimit?.status === "completed"
                          ? "Trade Completed"
                          : btnNamePeer}
                        {/* {btnName} */}
                      </div>
                      {timeLimit?.status !== "completed" && (
                        <div
                          className={styles.cancelBtn}
                          onClick={handleCancelTradePeer}
                        >
                          Cancel
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className={styles.leftBtns}>
                      <div
                        className={`${styles.notifySellerBtn} ${
                          timeLimit?.status === "completed" ||
                          timeLimit?.status === "expired"
                            ? styles.disabledBtn
                            : ""
                        }`}
                        onClick={
                          timeLimit?.status === "completed" ||
                          timeLimit?.status === "expired"
                            ? null
                            : sendPaymentFromSenderToPeer
                        }
                        style={{
                          opacity:
                            timeLimit?.status === "completed" ||
                            timeLimit?.status === "expired"
                              ? 0.5
                              : 1,
                          cursor:
                            timeLimit?.status === "completed" ||
                            timeLimit?.status === "expired"
                              ? "not-allowed"
                              : "pointer",
                        }}
                      >
                        {timeLimit?.status === "completed" || showReviewModal
                          ? "Trade Completed"
                          : btnNameUser}
                        {/* {btnName} */}
                      </div>
                      {timeLimit?.status !== "completed" && (
                        <div
                          className={styles.cancelBtn}
                          onClick={handleCancelTrade}
                        >
                          Cancel
                        </div>
                      )}
                    </div>
                  )}

                  <div className={styles.reportBtn} onClick={openModal}>
                    Report
                  </div>
                  <ReportTrade
                    title="Report Trade"
                    orderNumber={orderNumber}
                    isOpen={modalIsOpen}
                    onClose={closeModal}
                  />
                  <Modal
                    className={styles.modalWrapper}
                    isOpen={modalIsOpen2}
                    onAfterOpen={afterOpenModal2}
                    onRequestClose={closeModal2}
                    style={customStyles}
                    contentLabel="Example Modal"
                  >
                    <DisputeTicket setIsOpen2={setIsOpen2} />
                  </Modal>
                  <Modal
                    className={styles.modalWrapper}
                    isOpen={modalIsOpen3}
                    onAfterOpen={afterOpenModal3}
                    onRequestClose={closeModal3}
                    style={customStyles}
                    contentLabel="Example Modal"
                  >
                    {/* <UploadScreenShot
                          uploadPaymentEvidenceHandlerFromPeerToSender={
                            uploadPaymentEvidenceHandlerFromPeerToSender
                          }
                          setUploadEvidence={setUploadEvidence}
                          closeModal3={closeModal3}
                          subtitle={subtitle}
                          orderNumber={orderNumber}
                        /> */}
                  </Modal>
                  {/* Notify Peer Modal 👇 */}
                  {/* <Modal
                        className={styles.modalWrapper}
                        isOpen={modalIsOpen4}
                        onAfterOpen={afterOpenModal4}
                        onRequestClose={closeModal4}
                        style={customStyles}
                        contentLabel="Example Modal"
                      >
                        <h2
                          ref={(_subtitle) => (subtitle = _subtitle)}
                          className={styles.modalHeader}
                        >
                          Upload Payment screenshot
                          <svg
                            style={{ marginLeft: "30px", cursor: "pointer" }}
                            onClick={closeModal4}
                            xmlns="http://www.w3.org/2000/svg"
                            width="15"
                            height="16"
                            viewBox="0 0 15 16"
                            fill="none"
                     
                   
                        </h2>
                

                        <div className={styles.optionsBox}>
                          <div className={styles.inputUpload}>
                            <input
                              type="file"
                              onChange={(e) =>
                                setUploadEvidence(e.target.value)
                              }
                            />
                          </div>
                        </div>

                        <div className={styles.submitBtnWrapper}>
                          <button
                            className={styles.finalSubmitBtn}
                            onClick={
                              uploadPaymentEvidenceHandlerFromSenderToPeer
                            }
                          >
                            Submit
                          </button>
                        </div>
                      </Modal> */}
                  {/* Notify Peer Modal 👆 */}
                </div>
              </div>
              <div className={styles.bottomRightBox}>
                {/* <Chat /> */}

                <Chat Recipientname={username1} orderNumber={orderNumber} />

                {/* <Chat /> */}
              </div>
            </div>
          </div>
        </div>
        <Modal
          className={styles.modalWrapper}
          isOpen={showReviewModal}
          onRequestClose={() => setShowReviewModal(false)}
          style={customStyles}
          contentLabel="Trade Review Modal"
        >
          <TradeReviewModal
            onClose={() => setShowReviewModal(false)}
            orderNumber={orderNumber}
            username={username1 || "Trader"}
          />
        </Modal>
      </Layout>
    </>
  );
};

export default page;
