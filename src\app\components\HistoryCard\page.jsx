"use client";
import React, { useEffect, useState } from "react";
import styles from "./historyCard.module.css";
import { useRouter } from "next/navigation";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import { customFetchWithToken } from "@/app/utils/axiosInterpreter";

const HistoryCard = ({
  id,
  orderNo,
  dateCreated,
  status,
  trade_amount,
  indicative_fx_rate,
  listing_id,
  payin_currency_id__currency_code,
  payin_option_id__payment_method,
  payout_currency_id__currency_code,
  payout_option_id__payment_method,
  peer_id,
  max_liquidity,
  min_liquidity,
  availableLiquidity,
  terms,
  finalTradeFee,
}) => {
  const router = useRouter();
  const [isProcessing, setIsProcessing] = useState(false);

  // Enhanced date formatting with error handling
  const formatDateTime = (dateString) => {
    if (!dateString) return { date: "N/A", time: "N/A", relative: "Unknown" };

    try {
      const date = new Date(dateString);
      const formattedDate = date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
      const formattedTime = date.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: false,
      });

      // Calculate relative time
      const now = new Date();
      const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));
      let relative;

      if (diffInHours < 1) {
        relative = "Just now";
      } else if (diffInHours < 24) {
        relative = `${diffInHours}h ago`;
      } else {
        const diffInDays = Math.floor(diffInHours / 24);
        relative = `${diffInDays}d ago`;
      }

      return { date: formattedDate, time: formattedTime, relative };
    } catch (error) {
      console.error("Date formatting error:", error);
      return {
        date: "Invalid date",
        time: "Invalid time",
        relative: "Unknown",
      };
    }
  };

  const {
    date: formattedDate,
    time: formattedTime,
    relative: relativeTime,
  } = formatDateTime(dateCreated);

 



  const handlePeerDecisionToTradeAccept = async () => {
    if (isProcessing) return;

    setIsProcessing(true);
    const payload = {
      order_id: orderNo,
    };
    try {
      const response = await customFetchWithToken.post(
        "/trade/accept-request/",
        payload
      );

      // Show immediate feedback
      toast.info("Processing trade acceptance...");
      if (response.status === 200) {
        setTimeout(() => {
          router.push(`/pages/trade/${orderNo}`);
        }, 400);
      } else {
        toast.error(response.data.message);
        setIsProcessing(false);
      }
    } catch (error) {
      console.error("Error accepting trade:", error);
      toast.error("Failed to accept trade. Please try again.");
      setIsProcessing(false);
    }
  };

  const handlePeerDecisionToTradeReject = async () => {
    if (isProcessing) return;

    setIsProcessing(true);
    try {
      const payload = {
        order_id: orderNo,
      };
      toast.info("Processing trade rejection...");
      const response = await customFetchWithToken.post(
        "/trade/reject-request/",
        payload
      );
      if (response.status === 200) {
        setTimeout(() => {
          router.push(`/pages/trade/${orderNo}`);
        }, 400);
      } else {
        toast.error(response.data.message);
        setIsProcessing(false);
      }
    } catch (error) {
      console.error("Error rejecting trade:", error);
      toast.error("Failed to reject trade. Please try again.");
      setIsProcessing(false);
    }
  };

  const handleTradeById = async () => {
    if (isProcessing) return;

    setIsProcessing(true);
    try {
      const payload = {
        order_id: orderNo,
      };

      setTimeout(() => {
        router.push(`/pages/trade/${orderNo}`);
      }, 500);
    } catch (error) {
      console.error("Error navigating to trade:", error);
      toast.error("Failed to load trade details. Please try again.");
      setIsProcessing(false);
    }
  };

  const handleListingIdClick = () => {
    if (!listing_id) return;

    // Navigate to search ads page with listing ID as a URL parameter
    router.push(`/pages/searchads?listing_id=${listing_id}`);
  };

  const getStatusConfig = (status) => {
    const statusMap = {
      pending: {
        class: styles.statusPending,
        icon: "⏳",
        label: "Pending Approval",
        description: "Waiting for peer response",
      },
      expired: {
        class: styles.statusExpired,
        icon: "⏰",
        label: "Expired",
        description: "Trade request has expired",
      },
      ongoing: {
        class: styles.statusOngoing,
        icon: "🔄",
        label: "In Progress",
        description: "Trade is currently active",
      },
      rejected: {
        class: styles.statusRejected,
        icon: "❌",
        label: "Rejected",
        description: "Trade request was declined",
      },
      completed: {
        class: styles.statusCompleted,
        icon: "✅",
        label: "Completed",
        description: "Trade successfully finished",
      },
      Notified: {
        class: styles.statusNotified,
        icon: "🔔",
        label: "Notified",
        description: "Notification sent",
      },
      cancelled: {
        class: styles.statusCancelled,
        icon: "🚫",
        label: "Cancelled",
        description: "Trade was cancelled",
      },
    };

    return (
      statusMap[status] || {
        class: "",
        icon: "❓",
        label: status || "Unknown",
        description: "Status unknown",
      }
    );
  };

  const statusConfig = getStatusConfig(status);
  const formatCurrency = (amount, currency) => {
    if (!amount) return "N/A";
    return `${Number(amount).toLocaleString()} ${currency || ""}`;
  };

  return (
    <article
      className={styles.card}
      role="article"
      aria-labelledby={`order-${orderNo}`}
    >
      <header className={styles.cardHeader}>
        <div className={styles.orderInfo}>
          <span className={styles.label}>Order Number</span>
          <h3 id={`order-${orderNo}`} className={styles.orderNumber}>
            #{orderNo}
          </h3>
          <time
            className={styles.relativeTime}
            dateTime={dateCreated}
            title={`${formattedDate} at ${formattedTime}`}
          >
            {relativeTime}
          </time>
        </div>

        <div className={styles.statusContainer}>
          <div
            className={`${styles.statusBadge} ${statusConfig.class}`}
            title={statusConfig.description}
            aria-label={`Status: ${statusConfig.label}`}
          >
            <span className={styles.statusIcon} aria-hidden="true">
              {statusConfig.icon}
            </span>
            <span className={styles.statusText}>{statusConfig.label}</span>
          </div>

          <div className={styles.dateTimeContainer}>
            <time className={styles.date} dateTime={dateCreated}>
              {formattedDate}
            </time>
            <time className={styles.time} dateTime={dateCreated}>
              {formattedTime}
            </time>
          </div>
        </div>
      </header>

      <main className={styles.cardContent}>
        <div className={styles.infoGrid}>
          <section
            className={styles.infoSection}
            aria-labelledby="trade-details"
          >
            <h4 id="trade-details" className={styles.sectionTitle}>
              <span className={styles.sectionIcon} aria-hidden="true">
                💰
              </span>
              Trade Details
            </h4>
            <dl className={styles.infoList}>
              <div className={styles.infoRow}>
                <dt className={styles.infoLabel}>Trade Amount</dt>
                <dd className={styles.infoValue}>
                  {formatCurrency(
                    trade_amount,
                    payin_currency_id__currency_code
                  )}
                </dd>
              </div>
              <div className={styles.infoRow}>
                <dt className={styles.infoLabel}>Exchange Rate</dt>
                <dd className={styles.infoValue}>
                  {indicative_fx_rate
                    ? `1 ${payin_currency_id__currency_code} = ${Number(
                        indicative_fx_rate
                      ).toFixed(4)} ${payout_currency_id__currency_code}`
                    : "Rate not available"}
                </dd>
              </div>
              <div className={styles.infoRow}>
                <dt className={styles.infoLabel}>Listing ID</dt>
                <dd className={styles.infoValue}>
                  {listing_id ? (
                    <button
                      onClick={handleListingIdClick}
                      className={styles.listingIdLink}
                      title="Click to view this listing in Search Ads"
                      aria-label={`View listing ${listing_id} in Search Ads`}
                    >
                      #{listing_id}
                      <span className={styles.linkIcon} aria-hidden="true">
                        🔗
                      </span>
                    </button>
                  ) : (
                    "N/A"
                  )}
                </dd>
              </div>
              {/* {finalTradeFee && (
                <div className={styles.infoRow}>
                  <dt className={styles.infoLabel}>Trade Fee</dt>
                  <dd className={styles.infoValue}>{finalTradeFee}%</dd>
                </div>
              )} */}
              
            </dl>
          </section>

          <section
            className={styles.infoSection}
            aria-labelledby="payment-details"
          >
            <h4 id="payment-details" className={styles.sectionTitle}>
              <span className={styles.sectionIcon} aria-hidden="true">
                💳
              </span>
              Payment Details
            </h4>
            <dl className={styles.infoList}>
              <div className={styles.infoRow}>
                <dt className={styles.infoLabel}>Pay In</dt>
                <dd className={styles.infoValue}>
                  <span className={styles.currencyCode}>
                    {payin_currency_id__currency_code}
                  </span>
                </dd>
              </div>
              <div className={styles.infoRow}>
                <dt className={styles.infoLabel}>Pay In Method</dt>
                <dd className={styles.infoValue}>
                  {payin_option_id__payment_method}
                </dd>
              </div>
              <div className={styles.infoRow}>
                <dt className={styles.infoLabel}>Pay Out</dt>
                <dd className={styles.infoValue}>
                  <span className={styles.currencyCode}>
                    {payout_currency_id__currency_code}
                  </span>
                </dd>
              </div>
              <div className={styles.infoRow}>
                <dt className={styles.infoLabel}>Pay Out Method</dt>
                <dd className={styles.infoValue}>
                  {payout_option_id__payment_method}
                </dd>
              </div>
            </dl>
          </section>

          {/* {(peer_id ||
            availableLiquidity ||
            max_liquidity ||
            min_liquidity) && (
            <section
              className={styles.infoSection}
              aria-labelledby="liquidity-details"
            >
              <h4 id="liquidity-details" className={styles.sectionTitle}>
                <span className={styles.sectionIcon} aria-hidden="true">
                  🤝
                </span>
                Liquidity & Peer Info
              </h4>
              <dl className={styles.infoList}>
                {peer_id && (
                  <div className={styles.infoRow}>
                    <dt className={styles.infoLabel}>Peer ID</dt>
                    <dd className={styles.infoValue}>#{peer_id}</dd>
                  </div>
                )}
                {availableLiquidity && (
                  <div className={styles.infoRow}>
                    <dt className={styles.infoLabel}>Available Liquidity</dt>
                    <dd className={styles.infoValue}>
                      {formatCurrency(
                        availableLiquidity,
                        payin_currency_id__currency_code
                      )}
                    </dd>
                  </div>
                )}
                {min_liquidity && max_liquidity && (
                  <div className={styles.infoRow}>
                    <dt className={styles.infoLabel}>Liquidity Range</dt>
                    <dd className={styles.infoValue}>
                      {formatCurrency(
                        min_liquidity,
                        payin_currency_id__currency_code
                      )}{" "}
                      -{" "}
                      {formatCurrency(
                        max_liquidity,
                        payin_currency_id__currency_code
                      )}
                    </dd>
                  </div>
                )}
              </dl>
            </section>
          )} */}
        </div>
      </main>

      <footer className={styles.cardActions}>
        {status === "ongoing" && (
          <button
            onClick={handleTradeById}
            className={styles.primaryButton}
            disabled={isProcessing}
            aria-label={`Continue trade for order ${orderNo}`}
          >
            <span className={styles.buttonIcon} aria-hidden="true">
              🔄
            </span>
            {isProcessing ? "Loading..." : "Continue Trade"}
          </button>
        )}

        {status === "pending" && (
          <div
            className={styles.pendingActions}
            role="group"
            aria-labelledby="pending-actions"
          >
            <div id="pending-actions" className={styles.pendingMessage}>
              <span className={styles.messageIcon} aria-hidden="true">
                ⏳
              </span>
              Trade Awaiting Response
            </div>
            <div className={styles.buttonGroup}>
              <button
                className={styles.acceptButton}
                onClick={handlePeerDecisionToTradeAccept}
                disabled={isProcessing}
                aria-label={`Accept trade request for order ${orderNo}`}
              >
                <span className={styles.buttonIcon} aria-hidden="true">
                  ✅
                </span>
                {isProcessing ? "Processing..." : "Accept"}
              </button>
              <button
                className={styles.rejectButton}
                onClick={handlePeerDecisionToTradeReject}
                disabled={isProcessing}
                aria-label={`Reject trade request for order ${orderNo}`}
              >
                <span className={styles.buttonIcon} aria-hidden="true">
                  ❌
                </span>
                {isProcessing ? "Processing..." : "Reject"}
              </button>
            </div>
          </div>
        )}

        {status === "completed" && (
          <div className={styles.completedActions}>
            <div className={styles.completedMessage}>
              <span className={styles.messageIcon} aria-hidden="true">
                ✅
              </span>
              Trade Completed Successfully
            </div>
            <button
              onClick={handleTradeById}
              className={styles.secondaryButton}
              disabled={isProcessing}
              aria-label={`View details for completed trade ${orderNo}`}
            >
              <span className={styles.buttonIcon} aria-hidden="true">
                👁️
              </span>
              View Details
            </button>
          </div>
        )}

        {(status === "rejected" ||
          status === "expired" ||
          status === "cancelled") && (
          <div className={styles.inactiveActions}>
            <div className={styles.inactiveMessage}>
              <span className={styles.messageIcon} aria-hidden="true">
                {status === "rejected"
                  ? "❌"
                  : status === "expired"
                  ? "⏰"
                  : "🚫"}
              </span>
              Trade {status.charAt(0).toUpperCase() + status.slice(1)}
            </div>
          </div>
        )}
      </footer>
    </article>
  );
};

export default HistoryCard;
